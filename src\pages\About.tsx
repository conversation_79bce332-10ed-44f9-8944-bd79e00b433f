import { Card, CardContent } from '../components/ui/card'
import { Heart, Users, Leaf, Award } from 'lucide-react'

const About = () => {
  const teamMembers = [
    {
      id: 1,
      name: "Chef <PERSON>",
      role: "Head Chef",
      image: "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcSPMgkfiD2Woz0quB0scaRtAy0JWhuH-pup4A&s",
      description: "With 15 years of culinary experience, Chef <PERSON> brings authentic Tanzanian flavors to every dish."
    },
    {
      id: 2,
      name: "<PERSON>",
      role: "Restaurant Manager",
      image: "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcR1gDbL8i2KEclcYsSRe69iQm7TJF8xLj1ekQ&s",
      description: "<PERSON> ensures every guest has an exceptional dining experience with his attention to detail."
    },
    {
      id: 3,
      name: "<PERSON>",
      role: "Sous Chef",
      image: "https://media.istockphoto.com/id/1394055791/photo/portrait-of-confident-black-female-chef-at-restaurant-kitchen-looking-at-camera.jpg?s=612x612&w=0&k=20&c=vmDoulcE99YonSh-W70ZulSn6OV0MXSP_mO1PpYN5kM=",
      description: "Grace specializes in traditional recipes passed down through generations of Tanzanian families."
    }
  ]

  const values = [
    {
      icon: Heart,
      title: "Passion",
      description: "We pour our heart into every dish, creating memorable experiences for our guests."
    },
    {
      icon: Users,
      title: "Community",
      description: "We're proud to be part of the Mwanza community and support local farmers and suppliers."
    },
    {
      icon: Leaf,
      title: "Sustainability",
      description: "We source fresh, local ingredients and practice sustainable cooking methods."
    },
    {
      icon: Award,
      title: "Excellence",
      description: "We strive for excellence in every aspect of our service and culinary offerings."
    }
  ]

  return (
    <div className="min-h-screen bg-white">
      {/* Header */}
      <section className="bg-red-800 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-5xl font-serif font-bold mb-4">About LakeView Restaurant</h1>
          <p className="text-xl max-w-2xl mx-auto">
            A culinary journey through the heart of Tanzania
          </p>
        </div>
      </section>

      {/* Story Section */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-4xl font-serif font-bold text-gray-900 mb-6">Our Story</h2>
              <div className="space-y-4 text-gray-600 text-left">
                <p>
                  LakeView Restaurant was founded in 2010 with a simple vision: to bring the authentic 
                  flavors of Tanzania to both locals and visitors in a warm, welcoming environment. 
                  Located on the shores of beautiful Lake Victoria, our restaurant has become a beloved 
                  gathering place for the Mwanza community.
                </p>
                <p>
                  What started as a small family-owned establishment has grown into one of Mwanza's 
                  most celebrated dining destinations. We've maintained our commitment to using fresh, 
                  locally-sourced ingredients while honoring traditional Tanzanian cooking methods 
                  passed down through generations.
                </p>
                <p>
                  Today, we continue to serve our community with pride, offering not just exceptional 
                  food, but a place where memories are made, celebrations are held, and the rich 
                  culture of Tanzania is shared with every guest who walks through our doors.
                </p>
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <img 
                src="https://archicgi.com/wp-content/uploads/2024/02/3D-visualization-for-restaurant-interiors-base.jpg" 
                alt="Restaurant interior"
                className="rounded-lg shadow-xl shadow-black/60"
              />
              <img 
                src="https://assets.bonappetit.com/photos/5a8d9c058ca2430893f4e3c1/16:9/w_2560%2Cc_limit/life_on_the_line_1.jpg" 
                alt="Restaurant kitchen"
                className="rounded-lg shadow-xl shadow-black/60  md:mt-8"
              />
              <img 
                src="https://www.tanzaniatourism.com/images/uploads/Lake_Victoria_Bismark_Rocks_2.jpg" 
                alt="Lake Victoria view"
                className="rounded-lg shadow-xl shadow-black/60 md:-mt-8"
              />
              <img 
                src="https://static01.nyt.com/images/2022/01/19/dining/19restaurant-exchange3/merlin_199610832_1f05a894-6228-4911-8602-8185c91794c1-articleLarge.jpg?quality=75&auto=webp&disable=upscale" 
                alt="Restaurant dining"
                className="rounded-lg shadow-xl shadow-black/60"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Mission Statement */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-4xl font-serif font-bold text-gray-900 mb-8">Our Mission</h2>
          <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
            To provide an exceptional dining experience that celebrates the rich culinary heritage 
            of Tanzania while fostering community connections and supporting local agriculture. 
            We are committed to quality, hospitality, and creating lasting memories for every guest.
          </p>
        </div>
      </section>

      {/* Values */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-4xl font-serif font-bold text-gray-900 text-center mb-12">Our Values</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {values.map((value, index) => (
              <Card key={index} className="text-center p-6 hover:shadow-lg transition-shadow duration-300">
                <CardContent className="p-0">
                  <value.icon className="w-12 h-12 text-red-800 mx-auto mb-4" />
                  <h3 className="text-xl font-semibold mb-3">{value.title}</h3>
                  <p className="text-gray-600">{value.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Team Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <h2 className="text-4xl font-serif font-bold text-gray-900 text-center mb-12">Meet Our Team</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {teamMembers.map((member) => (
              <Card key={member.id} className="overflow-hidden hover:shadow-lg transition-shadow duration-300">
                <img 
                  src={member.image} 
                  alt={member.name}
                  className="w-full h-64 object-cover"
                />
                <CardContent className="p-6 text-center">
                  <h3 className="text-xl font-semibold mb-1">{member.name}</h3>
                  <p className="text-red-800 font-medium mb-3">{member.role}</p>
                  <p className="text-gray-600">{member.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Awards & Recognition */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-4xl font-serif font-bold text-gray-900 mb-8">Awards & Recognition</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="p-6">
              <Award className="w-16 h-16 text-yellow-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-2">Best Restaurant 2023</h3>
              <p className="text-gray-600">Mwanza Tourism Board</p>
            </div>
            <div className="p-6">
              <Award className="w-16 h-16 text-yellow-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-2">Excellence in Service</h3>
              <p className="text-gray-600">Tanzania Restaurant Association</p>
            </div>
            <div className="p-6">
              <Award className="w-16 h-16 text-yellow-400 mx-auto mb-4" />
              <h3 className="text-xl font-semibold mb-2">Community Choice Award</h3>
              <p className="text-gray-600">Local Business Awards 2022</p>
            </div>
          </div>
        </div>
      </section>
    </div>
  )
}

export default About

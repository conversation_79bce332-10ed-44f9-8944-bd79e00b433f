import { <PERSON> } from 'react-router-dom'
import { But<PERSON> } from '../components/ui/button'
import { Card, CardContent } from '../components/ui/card'
import { Star, Clock, Users } from 'lucide-react'

const Home = () => {
  const featuredDishes = [
    {
      id: 1,
      name: "Grilled Tilapia",
      description: "Fresh tilapia from Lake Victoria, grilled to perfection with local spices",
      image: "https://images.pexels.com/photos/725991/pexels-photo-725991.jpeg",
      price: "TSh 25,000"
    },
    {
      id: 2,
      name: "<PERSON><PERSON> Choma",
      description: "Traditional Tanzanian grilled meat served with ugali and vegetables",
      image: "https://images.pexels.com/photos/410648/pexels-photo-410648.jpeg",
      price: "TSh 30,000"
    },
    {
      id: 3,
      name: "Pilau Rice",
      description: "Aromatic spiced rice cooked with tender beef and traditional spices",
      image: "https://images.pexels.com/photos/7593267/pexels-photo-7593267.jpeg",
      price: "TSh 20,000"
    },
    {
      id: 4,
      name: "Coconut Curry",
      description: "Rich coconut curry with fresh vegetables and your choice of protein",
      image: "https://spicecravings.com/wp-content/uploads/2022/10/Coconut-Chicken-Featured-2.jpg",
      price: "TSh 22,000"
    }
  ]

  const testimonials = [
    {
      id: 1,
      name: "Sarah Johnson",
      rating: 5,
      comment: "Amazing food and atmosphere! The tilapia was incredibly fresh and the service was outstanding.",
      location: "Mwanza"
    },
    {
      id: 2,
      name: "David Mwalimu",
      rating: 5,
      comment: "Best restaurant in Mwanza! The traditional dishes are authentic and delicious.",
      location: "Dar es Salaam"
    },
    {
      id: 3,
      name: "Emma Thompson",
      rating: 5,
      comment: "Perfect place for a romantic dinner. The lake view is breathtaking and food is exceptional.",
      location: "Arusha"
    }
  ]

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section 
        className="relative h-screen bg-cover bg-center bg-no-repeat"
        style={{
          backgroundImage: "linear-gradient(rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.4)), url('https://images.pexels.com/photos/958545/pexels-photo-958545.jpeg')"
        }}
      >
        {/* blurry overlay */}
        <div className="absolute inset-0 bg-black/30 backdrop-blur-[3px]"></div>
        {/* content */}
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-center text-white px-4">
            <h1 className="text-5xl md:text-7xl text-red-500 font-serif font-bold mb-6 animate-fade-in">
              Taste the Best of Mwanza
            </h1>
            <p className="text-xl md:text-2xl mb-8 max-w-2xl mx-auto">
              Fresh, local ingredients cooked with passion
            </p>
            <div className="flex flex-col md:flex-row items-center md:items-center md:justify-center gap-4 animate-fade-in">
              <Link to="/menu">
                <Button size="lg" className="bg-red-800 hover:bg-red-900 text-white px-8 py-3">
                  View Menu
                </Button>
              </Link>
              <Link to="/reservations">
                <Button size="lg" variant="outline" className="border-white border-[1px] bg-white/10 backdrop-blur-sm text-white hover:bg-white hover:text-red-800 px-8 py-3">
                  Reserve a Table
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Dishes Section */}
      <section className="py-16 bg-gradient-to-br from-red-800/10 to-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-4xl font-serif font-bold text-gray-900 mb-4">
              Featured Dishes
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Discover our most popular dishes, crafted with the finest local ingredients
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {featuredDishes.map((dish, index) => (
              <Card
                key={dish.id}
                className="overflow-hidden hover:shadow-xl transition-all duration-300 hover:-translate-y-2 animate-slide-up"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="aspect-w-16 aspect-h-12 relative overflow-hidden">
                  <img
                    src={dish.image}
                    alt={dish.name}
                    className="w-full h-48 object-cover transition-transform duration-300 hover:scale-110"
                  />
                  <div className="absolute inset-0 bg-black/30 hover:bg-black/10 transition-all duration-300"></div>
                </div>
                <CardContent className="p-6">
                  <h3 className="text-xl font-semibold mb-2">{dish.name}</h3>
                  <p className="text-gray-600 mb-3">{dish.description}</p>
                  <div className="flex justify-between items-center">
                    <span className="text-lg font-bold text-red-800">{dish.price}</span>
                    <Button size="sm" className="bg-red-800 hover:bg-red-900 transition-all duration-200 hover:scale-105">
                      Order Now
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Customer Testimonials */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-4xl font-serif font-bold text-gray-900 mb-4">
              What Our Customers Say
            </h2>
            <p className="text-lg text-gray-600">
              Don't just take our word for it
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {testimonials.map((testimonial, index) => (
              <Card
                key={testimonial.id}
                className="p-6 hover:shadow-lg transition-all duration-300 hover:-translate-y-1 animate-scale-in"
                style={{ animationDelay: `${index * 0.2}s` }}
              >
                <CardContent className="p-0">
                  <div className="flex mb-4">
                    {[...Array(testimonial.rating)].map((_, i) => (
                      <Star
                        key={i}
                        className="w-5 h-5 text-yellow-400 fill-current transition-transform duration-200 hover:scale-125"
                      />
                    ))}
                  </div>
                  <p className="text-gray-600 mb-4 italic">"{testimonial.comment}"</p>
                  <div>
                    <p className="font-semibold">{testimonial.name}</p>
                    <p className="text-sm text-gray-500">{testimonial.location}</p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Special Offers */}
      <section className="py-16 bg-red-800 text-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-4xl font-serif font-bold mb-4">
              Special Offers
            </h2>
            <p className="text-lg">
              Don't miss out on our amazing deals
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <Card className="bg-yellow-400 text-gray-900 p-8 hover:shadow-xl transition-all duration-300 hover:-translate-y-2 animate-slide-up">
              <CardContent className="p-0 text-center">
                <Clock className="w-12 h-12 mx-auto mb-4 transition-transform duration-300 hover:rotate-12" />
                <h3 className="text-2xl font-bold mb-2">Happy Hour</h3>
                <p className="mb-4">50% off all drinks from 5-7 PM daily</p>
                <Button className="bg-red-800 hover:bg-red-900 text-white transition-all duration-200 hover:scale-105">
                  Learn More
                </Button>
              </CardContent>
            </Card>

            <Card className="bg-white text-gray-900 p-8 hover:shadow-xl transition-all duration-300 hover:-translate-y-2 animate-slide-up" style={{ animationDelay: '0.1s' }}>
              <CardContent className="p-0 text-center">
                <Users className="w-12 h-12 mx-auto mb-4 text-red-800 transition-transform duration-300 hover:scale-110" />
                <h3 className="text-2xl font-bold mb-2">Group Dining</h3>
                <p className="mb-4">Special packages for groups of 8 or more</p>
                <Button className="bg-red-800 hover:bg-red-900 text-white transition-all duration-200 hover:scale-105">
                  Book Now
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>
    </div>
  )
}

export default Home

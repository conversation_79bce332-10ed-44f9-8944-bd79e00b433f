import { Badge } from '../components/ui/badge'
import { Card, CardContent } from '../components/ui/card'
import { <PERSON><PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '../components/ui/tabs'
import { Crown } from 'lucide-react'

const Menu = () => {
  const menuSections = {
    starters: [
      {
        id: 1,
        name: "<PERSON><PERSON><PERSON>",
        description: "Crispy pastries filled with spiced vegetables or meat",
        price: "TSh 8,000",
        image: "https://static.toiimg.com/photo/61050397.cms",
        isSpecial: false
      },
      {
        id: 2,
        name: "Chapati Rolls",
        description: "Soft chapati wrapped with seasoned chicken and vegetables",
        price: "TSh 12,000",
        image: "https://tarasmulticulturaltable.com/wp-content/uploads/2018/05/Chapati-Za-Ngozi-Kenyan-Soft-Layered-Flatbread-3-of-3.jpg",
        isSpecial: true
      },
      {
        id: 3,
        name: "Soup of the Day",
        description: "Fresh seasonal soup made with local ingredients",
        price: "TSh 10,000",
        image: "https://images.pexels.com/photos/8953854/pexels-photo-8953854.jpeg",
        isSpecial: false
      },
      {
        id: 4,
        name: "Grilled Prawns",
        description: "Fresh prawns from Lake Victoria, grilled with garlic and herbs",
        price: "TSh 18,000",
        image: "https://images.pexels.com/photos/725997/pexels-photo-725997.jpeg",
        isSpecial: true
      }
    ],
    mains: [
      {
        id: 5,
        name: "Grilled Tilapia",
        description: "Fresh tilapia from Lake Victoria, served with rice and vegetables",
        price: "TSh 25,000",
        image: "https://images.pexels.com/photos/555775/pexels-photo-555775.jpeg",
        isSpecial: true
      },
      {
        id: 6,
        name: "Nyama Choma",
        description: "Traditional grilled meat served with ugali and sukuma wiki",
        price: "TSh 30,000",
        image: "https://images.pexels.com/photos/410648/pexels-photo-410648.jpeg",
        isSpecial: false
      },
      {
        id: 7,
        name: "Pilau Rice with Chicken",
        description: "Aromatic spiced rice with tender chicken pieces",
        price: "TSh 22,000",
        image: "https://img.freepik.com/free-photo/side-view-pilaf-with-stewed-beef-meat-plate_141793-5057.jpg",
        isSpecial: false
      },
      {
        id: 8,
        name: "Coconut Fish Curry",
        description: "Fresh fish cooked in rich coconut curry sauce",
        price: "TSh 28,000",
        image: "https://www.indianhealthyrecipes.com/wp-content/uploads/2021/10/fish-curry-recipe.jpg",
        isSpecial: true
      },
      {
        id: 9,
        name: "Beef Stew",
        description: "Tender beef slow-cooked with vegetables and spices",
        price: "TSh 26,000",
        image: "https://hips.hearstapps.com/hmg-prod/images/beef-stew-index-652e94c53b39b.jpg?crop=0.8891301070405547xw:1xh;center,top&resize=1200:*",
        isSpecial: false
      },
      {
        id: 10,
        name: "Vegetarian Curry",
        description: "Mixed vegetables in aromatic curry sauce with rice",
        price: "TSh 18,000",
        image: "https://images.immediate.co.uk/production/volatile/sites/30/2020/08/slow-cooker-vegetable-curry-2b72ddd.jpg?quality=90",
        isSpecial: false
      }
    ],
    desserts: [
      {
        id: 11,
        name: "Mandazi",
        description: "Traditional East African sweet bread, served warm",
        price: "TSh 5,000",
        image: "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcRbeXdkLVLLrUF3QJox3gUVZICWw32P5XlV0g&s",
        isSpecial: false
      },
      {
        id: 12,
        name: "Coconut Ice Cream",
        description: "Homemade coconut ice cream with tropical fruit",
        price: "TSh 8,000",
        image: "https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcS4w0RilBMqtdSrsJfN025t5CkzmeqRfGqNzw&s",
        isSpecial: true
      },
      {
        id: 13,
        name: "Fruit Salad",
        description: "Fresh seasonal fruits with honey and mint",
        price: "TSh 7,000",
        image: "https://fortheloveofcooking.net/wp-content/uploads/2013/05/DSC_4449.1.jpg",
        isSpecial: false
      },
      {
        id: 14,
        name: "Chocolate Cake",
        description: "Rich chocolate cake with vanilla cream",
        price: "TSh 10,000",
        image: "https://ichef.bbci.co.uk/food/ic/food_16x9_1600/recipes/easy_chocolate_cake_31070_16x9.jpg",
        isSpecial: false
      }
    ],
    drinks: [
      {
        id: 15,
        name: "Fresh Juice",
        description: "Orange, mango, or passion fruit juice",
        price: "TSh 6,000",
        image: "https://t4.ftcdn.net/jpg/01/39/46/07/360_F_139460703_68ql2mESojJSBBLq9aM8NnCR6En2QLaM.jpg",
        isSpecial: false
      },
      {
        id: 16,
        name: "Tanzanian Coffee",
        description: "Premium local coffee beans, freshly brewed",
        price: "TSh 4,000",
        image: "https://static.vecteezy.com/system/resources/thumbnails/025/282/026/small/stock-of-mix-a-cup-coffee-latte-more-motive-top-view-foodgraphy-generative-ai-photo.jpg",
        isSpecial: true
      },
      {
        id: 17,
        name: "Chai Tea",
        description: "Traditional spiced tea with milk",
        price: "TSh 3,000",
        image: "https://www.foodandwine.com/thmb/6wTm7a0y87X97LK-ZMxe2787kI8=/1500x0/filters:no_upscale():max_bytes(150000):strip_icc()/different-types-of-tea-FT-BLOG0621-7c7fd231e66d4fea8ca9a47cad52ba79.jpg",
        isSpecial: false
      },
      {
        id: 18,
        name: "Local Beer",
        description: "Kilimanjaro or Safari beer",
        price: "TSh 8,000",
        image: "https://thumbs.dreamstime.com/b/bottles-famous-global-beer-brands-poznan-pol-mar-including-heineken-becks-bud-miller-corona-stella-artois-san-miguel-143170511.jpg",
        isSpecial: false
      },
      {
        id: 19,
        name: "Wine Selection",
        description: "Red or white wine from our curated selection",
        price: "TSh 15,000",
        image: "https://www.thinkitchen.in/cdn/shop/articles/11_Types_of_Wine.jpg?v=1711203288&width=500",
        isSpecial: false
      },
      {
        id: 20,
        name: "Signature Cocktail",
        description: "LakeView special cocktail with tropical fruits",
        price: "TSh 12,000",
        image: "https://www.sainsburysmagazine.co.uk/uploads/media/2400x1800/06/11036-260820_Sainsburys-Mag_Light-My-Fire_Cocktail.jpg?v=1-0",
        isSpecial: true
      }
    ]
  }

  const MenuCard = ({ item }: { item: any }) => (
    <Card className="overflow-hidden hover:shadow-lg transition-shadow duration-300">
      <div className="relative">
        <img 
          src={item.image} 
          alt={item.name}
          className="w-full h-48 object-cover"
        />
        {item.isSpecial && (
          <Badge className="absolute top-2 right-2 bg-yellow-400 text-gray-900">
            <Crown className="w-3 h-3 mr-1" />
            Chef's Special
          </Badge>
        )}
      </div>
      <CardContent className="p-4">
        <div className="flex justify-between items-start mb-2">
          <h3 className="text-lg font-semibold">{item.name}</h3>
          <span className="text-lg font-bold text-red-800">{item.price}</span>
        </div>
        <p className="text-gray-600 text-sm">{item.description}</p>
      </CardContent>
    </Card>
  )

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <section className="bg-red-800 text-white py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h1 className="text-5xl font-serif font-bold mb-4">Our Menu</h1>
          <p className="text-xl max-w-2xl mx-auto">
            Discover the authentic flavors of Tanzania with our carefully crafted dishes
          </p>
        </div>
      </section>

      {/* Menu Content */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <Tabs defaultValue="starters" className="w-full">
            <TabsList className="grid w-full grid-cols-2 md:grid-cols-4 mb-8">
              <TabsTrigger value="starters">Starters</TabsTrigger>
              <TabsTrigger value="mains">Main Courses</TabsTrigger>
              <TabsTrigger value="desserts">Desserts</TabsTrigger>
              <TabsTrigger value="drinks">Drinks</TabsTrigger>
            </TabsList>

            <TabsContent value="starters">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {menuSections.starters.map((item) => (
                  <MenuCard key={item.id} item={item} />
                ))}
              </div>
            </TabsContent>

            <TabsContent value="mains">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {menuSections.mains.map((item) => (
                  <MenuCard key={item.id} item={item} />
                ))}
              </div>
            </TabsContent>

            <TabsContent value="desserts">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {menuSections.desserts.map((item) => (
                  <MenuCard key={item.id} item={item} />
                ))}
              </div>
            </TabsContent>

            <TabsContent value="drinks">
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {menuSections.drinks.map((item) => (
                  <MenuCard key={item.id} item={item} />
                ))}
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </section>
    </div>
  )
}

export default Menu
